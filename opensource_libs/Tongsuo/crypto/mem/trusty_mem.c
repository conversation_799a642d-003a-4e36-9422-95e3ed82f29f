/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo Trusty内存管理适配层
 * 为TEE环境提供安全的内存管理功能
 */

#include <openssl/crypto.h>
/* 不包含 openssl/mem.h 以避免与BoringSSL冲突 */

#if defined(__TRUSTY__) && defined(__TEE__)

#include <stdlib.h>
#include <string.h>
#include <stdint.h>

/* 手动声明需要的函数以避免头文件冲突 */
void *CRYPTO_malloc(size_t num, const char *file, int line);
void CRYPTO_free(void *ptr, const char *file, int line);
void *CRYPTO_realloc(void *addr, size_t num, const char *file, int line);
char *CRYPTO_strdup(const char *str, const char *file, int line);
char *CRYPTO_strndup(const char *str, size_t s, const char *file, int line);

/*
 * Tongsuo Trusty环境特定的内存管理函数
 * 这些函数只在Trusty环境中使用，避免与主库冲突
 */

#if defined(__TRUSTY__)

/*
 * Trusty环境的内存分配函数（简化版本，不使用调试信息）
 */
void *TONGSUO_OPENSSL_malloc(size_t size) {
    if (size == 0) {
        return NULL;
    }
    return malloc(size);
}

/*
 * Trusty环境的内存重新分配函数
 */
void *TONGSUO_OPENSSL_realloc(void *ptr, size_t size) {
    if (size == 0) {
        if (ptr != NULL) {
            free(ptr);
        }
        return NULL;
    }
    return realloc(ptr, size);
}

/*
 * Trusty环境的内存释放函数
 */
void TONGSUO_OPENSSL_free(void *ptr) {
    if (ptr != NULL) {
        free(ptr);
    }
}

/*
 * Trusty环境的安全内存重新分配函数
 * 类似于BoringSSL的OPENSSL_clear_free，在重新分配时清零旧内存
 */
void *TONGSUO_CRYPTO_clear_realloc(void *ptr, size_t old_len, size_t num,
                                   const char *file, int line) {
    /* 在TEE环境中忽略调试信息 */
    (void)file;
    (void)line;

    if (ptr == NULL) {
        return TONGSUO_OPENSSL_malloc(num);
    }

    if (num == 0) {
        if (ptr != NULL) {
            TONGSUO_OPENSSL_cleanse(ptr, old_len);
            TONGSUO_OPENSSL_free(ptr);
        }
        return NULL;
    }

    void *new_ptr = TONGSUO_OPENSSL_malloc(num);
    if (new_ptr == NULL) {
        return NULL;
    }

    /* 复制数据到新内存 */
    size_t copy_len = (old_len < num) ? old_len : num;
    if (copy_len > 0) {
        memcpy(new_ptr, ptr, copy_len);
    }

    /* 清零并释放旧内存 */
    TONGSUO_OPENSSL_cleanse(ptr, old_len);
    TONGSUO_OPENSSL_free(ptr);

    return new_ptr;
}

/*
 * Trusty环境的安全内存释放函数
 */
void TONGSUO_CRYPTO_clear_free(void *ptr, size_t num, const char *file, int line) {
    /* 在TEE环境中忽略调试信息 */
    (void)file;
    (void)line;

    if (ptr != NULL) {
        if (num > 0) {
            TONGSUO_OPENSSL_cleanse(ptr, num);
        }
        TONGSUO_OPENSSL_free(ptr);
    }
}

/*
 * Trusty环境的内存分配函数（带调试信息）
 */
void *TONGSUO_CRYPTO_malloc(size_t num, const char *file, int line) {
    /* 在TEE环境中忽略调试信息 */
    (void)file;
    (void)line;
    return TONGSUO_OPENSSL_malloc(num);
}

/*
 * Trusty环境的内存重新分配函数（带调试信息）
 */
void *TONGSUO_CRYPTO_realloc(void *addr, size_t num, const char *file, int line) {
    /* 在TEE环境中忽略调试信息 */
    (void)file;
    (void)line;
    return TONGSUO_OPENSSL_realloc(addr, num);
}

/*
 * Trusty环境的内存释放函数（带调试信息）
 */
void TONGSUO_CRYPTO_free(void *ptr, const char *file, int line) {
    /* 在TEE环境中忽略调试信息 */
    (void)file;
    (void)line;
    TONGSUO_OPENSSL_free(ptr);
}

/*
 * Trusty环境的零初始化内存分配函数
 */
void *TONGSUO_CRYPTO_zalloc(size_t num, const char *file, int line) {
    /* 在TEE环境中忽略调试信息 */
    (void)file;
    (void)line;

    void *ptr = TONGSUO_OPENSSL_malloc(num);
    if (ptr != NULL && num > 0) {
        memset(ptr, 0, num);
    }
    return ptr;
}

#endif /* __TRUSTY__ */

/*
 * Tongsuo安全内存清零函数
 */
void TONGSUO_OPENSSL_cleanse(void *ptr, size_t len) {
    if (ptr == NULL || len == 0) {
        return;
    }
    
    /* 使用volatile防止编译器优化掉内存清零操作 */
    volatile unsigned char *p = (volatile unsigned char *)ptr;
    while (len--) {
        *p++ = 0;
    }
}

/*
 * Tongsuo字符串复制函数
 */
char *TONGSUO_OPENSSL_strdup(const char *str) {
    if (str == NULL) {
        return NULL;
    }

    size_t len = strlen(str) + 1;
    char *copy = TONGSUO_OPENSSL_malloc(len);
    if (copy != NULL) {
        memcpy(copy, str, len);
    }
    return copy;
}

/*
 * Tongsuo内存比较函数（常量时间）
 */
int TONGSUO_CRYPTO_memcmp(const void *a, const void *b, size_t len) {
    if (a == NULL || b == NULL) {
        return (a == b) ? 0 : 1;
    }
    
    const unsigned char *pa = (const unsigned char *)a;
    const unsigned char *pb = (const unsigned char *)b;
    unsigned char result = 0;
    
    /* 常量时间比较，防止时序攻击 */
    for (size_t i = 0; i < len; i++) {
        result |= pa[i] ^ pb[i];
    }
    
    return result ? 1 : 0;
}

/*
 * Tongsuo安全内存分配函数（带清零）
 */
void *TONGSUO_OPENSSL_zalloc(size_t size) {
    void *ptr = TONGSUO_OPENSSL_malloc(size);
    if (ptr != NULL) {
        memset(ptr, 0, size);
    }
    return ptr;
}

/*
 * Tongsuo内存复制函数
 */
void *TONGSUO_OPENSSL_memdup(const void *data, size_t size) {
    if (data == NULL || size == 0) {
        return NULL;
    }

    void *copy = TONGSUO_OPENSSL_malloc(size);
    if (copy != NULL) {
        memcpy(copy, data, size);
    }
    return copy;
}

/*
 * Tongsuo字符串长度限制复制函数
 */
char *TONGSUO_OPENSSL_strndup(const char *str, size_t maxlen) {
    if (str == NULL) {
        return NULL;
    }

    size_t len = 0;
    while (len < maxlen && str[len] != '\0') {
        len++;
    }

    char *copy = TONGSUO_OPENSSL_malloc(len + 1);
    if (copy != NULL) {
        memcpy(copy, str, len);
        copy[len] = '\0';
    }
    return copy;
}

/*
 * Trusty环境特定的内存管理配置函数
 * 这些函数只在需要时提供，避免与主库冲突
 */

#if defined(__TRUSTY__)

/*
 * 在Trusty环境中不允许更改内存分配函数
 */
int TONGSUO_CRYPTO_set_mem_functions(
    CRYPTO_malloc_fn malloc_fn,
    CRYPTO_realloc_fn realloc_fn,
    CRYPTO_free_fn free_fn) {

    /* 在Trusty环境中不允许更改内存分配函数 */
    (void)malloc_fn;
    (void)realloc_fn;
    (void)free_fn;
    return 0;
}

#endif /* __TRUSTY__ */

#endif  /* __TRUSTY__ && __TEE__ */
