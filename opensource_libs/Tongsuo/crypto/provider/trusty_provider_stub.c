/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo Trusty Provider stub函数
 * 为TEE环境提供简化的provider实现
 */

#if defined(__TRUSTY__) && defined(__TEE__)

#include <openssl/provider.h>
#include <openssl/core.h>

/*
 * Provider初始化函数的stub实现
 * 在TEE环境中，我们使用简化的provider模型
 */

int TONGSUO_ossl_null_provider_init(const OSSL_CORE_HANDLE *handle,
                                   const OSSL_DISPATCH *in,
                                   const OSSL_DISPATCH **out,
                                   void **provctx) {
    /* 在TEE环境中，null provider不需要实际初始化 */
    (void)handle;
    (void)in;
    (void)out;
    (void)provctx;
    return 1;  /* 成功 */
}

int TONGSUO_ossl_default_provider_init(const OSSL_CORE_HANDLE *handle,
                                      const OSSL_DISPATCH *in,
                                      const OSSL_DISPATCH **out,
                                      void **provctx) {
    /* 在TEE环境中，default provider使用简化实现 */
    (void)handle;
    (void)in;
    (void)out;
    (void)provctx;
    return 1;  /* 成功 */
}

int TONGSUO_ossl_base_provider_init(const OSSL_CORE_HANDLE *handle,
                                   const OSSL_DISPATCH *in,
                                   const OSSL_DISPATCH **out,
                                   void **provctx) {
    /* 在TEE环境中，base provider使用简化实现 */
    (void)handle;
    (void)in;
    (void)out;
    (void)provctx;
    return 1;  /* 成功 */
}

/*
 * 随机数池管理函数的stub实现
 */

void TONGSUO_ossl_rand_pool_cleanup(void) {
    /* TEE环境中的随机数池清理是空操作 */
}

int TONGSUO_ossl_rand_pool_init(void) {
    /* TEE环境中的随机数池初始化总是成功 */
    return 1;
}

/*
 * 错误状态设置函数的stub实现
 */

void TONGSUO_ossl_set_error_state(const char *type) {
    /* 在TEE环境中，错误状态设置是空操作 */
    (void)type;
}

/*
 * 自测试回调函数的stub实现
 */

void *TONGSUO_OSSL_SELF_TEST_get_callback(void *libctx) {
    /* TEE环境中不需要自测试回调 */
    (void)libctx;
    return NULL;
}

/*
 * 椭圆曲线相关函数的stub实现
 */

void TONGSUO_ossl_x448_public_from_private(unsigned char public_key[56],
                                          const unsigned char private_key[56]) {
    /* TEE环境中暂不支持X448 */
    (void)public_key;
    (void)private_key;
}

void TONGSUO_ossl_ed448_public_from_private(unsigned char public_key[57],
                                           const unsigned char private_key[57]) {
    /* TEE环境中暂不支持Ed448 */
    (void)public_key;
    (void)private_key;
}

/*
 * 错误字符串加载函数的stub实现
 */

void TONGSUO_ossl_err_load_PROV_strings(void) {
    /* TEE环境中不需要加载错误字符串 */
}

/*
 * 随机数池相关函数的stub实现
 */

size_t TONGSUO_ossl_pool_acquire_entropy(void *pool) {
    /* TEE环境中使用简化的随机数实现 */
    (void)pool;
    return 0;
}

size_t TONGSUO_ossl_pool_add_nonce_data(void *pool) {
    /* TEE环境中使用简化的随机数实现 */
    (void)pool;
    return 0;
}

#endif /* __TRUSTY__ && __TEE__ */
