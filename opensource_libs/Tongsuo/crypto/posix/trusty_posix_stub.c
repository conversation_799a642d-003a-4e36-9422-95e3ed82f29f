/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo Trusty POSIX stub函数
 * 为TEE环境提供不支持的POSIX函数的stub实现
 */

#if defined(__TRUSTY__) && defined(__TEE__)

#include <sys/types.h>
#include <dirent.h>
#include <unistd.h>
#include <errno.h>

/*
 * 用户ID相关函数的stub实现
 * 在TEE环境中，这些概念不适用，返回固定值
 */

uid_t getuid(void) {
    /* TEE环境中没有用户ID概念，返回0 */
    return 0;
}

uid_t geteuid(void) {
    /* TEE环境中没有有效用户ID概念，返回0 */
    return 0;
}

gid_t getgid(void) {
    /* TEE环境中没有组ID概念，返回0 */
    return 0;
}

gid_t getegid(void) {
    /* TEE环境中没有有效组ID概念，返回0 */
    return 0;
}

/*
 * 目录操作函数的stub实现
 * TEE环境通常没有文件系统访问权限
 */

DIR *opendir(const char *name) {
    /* TEE环境中不支持目录操作 */
    (void)name;
    errno = ENOENT;  /* 目录不存在 */
    return NULL;
}

struct dirent *readdir(DIR *dirp) {
    /* TEE环境中不支持目录读取 */
    (void)dirp;
    errno = EBADF;   /* 无效的文件描述符 */
    return NULL;
}

int closedir(DIR *dirp) {
    /* TEE环境中不支持目录操作 */
    (void)dirp;
    errno = EBADF;   /* 无效的文件描述符 */
    return -1;
}

#endif /* __TRUSTY__ && __TEE__ */
