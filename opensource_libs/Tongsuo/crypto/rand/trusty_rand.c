/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo Trusty随机数适配层
 * 参考BoringSSL的trusty.c实现
 */

#include <openssl/rand.h>
#include <openssl/crypto.h>

#if defined(__TRUSTY__) && defined(__TEE__)

#include <stdint.h>
#include <stdlib.h>
#include <sys/types.h>
#include <uapi/err.h>
#include <lib/rng/trusty_rng.h>

/*
 * Tongsuo随机数生成函数
 * 对接Trusty的硬件随机数生成器
 */
int TONGSUO_RAND_bytes(unsigned char *buf, int num) {
    if (num < 0) {
        return 0;
    }
    
    if (num == 0) {
        return 1;
    }
    
    if (trusty_rng_hw_rand(buf, (size_t)num) != NO_ERROR) {
        return 0;
    }
    
    return 1;
}

/*
 * Tongsuo伪随机数生成函数
 * 在TEE环境中，我们使用相同的硬件随机数生成器
 */
int TONGSUO_RAND_pseudo_bytes(unsigned char *buf, int num) {
    return TONGSUO_RAND_bytes(buf, num);
}

/*
 * 添加随机种子（在TEE环境中通常不需要）
 */
void TONGSUO_RAND_seed(const void *buf, int num) {
    /* 在Trusty环境中，硬件随机数生成器不需要额外的种子 */
    (void)buf;
    (void)num;
}

/*
 * 添加随机熵（在TEE环境中通常不需要）
 */
int TONGSUO_RAND_add(const void *buf, int num, double randomness) {
    /* 在Trusty环境中，硬件随机数生成器不需要额外的熵 */
    (void)buf;
    (void)num;
    (void)randomness;
    return 1;
}

/*
 * 检查随机数生成器状态
 */
int TONGSUO_RAND_status(void) {
    /* 在Trusty环境中，硬件随机数生成器总是可用的 */
    return 1;
}

/*
 * 清理随机数生成器状态（在TEE环境中通常不需要）
 */
void TONGSUO_RAND_cleanup(void) {
    /* 在Trusty环境中不需要清理 */
}

/*
 * 设置随机数生成方法（在TEE环境中使用固定的硬件实现）
 */
int TONGSUO_RAND_set_rand_method(const RAND_METHOD *meth) {
    /* 在Trusty环境中不允许更改随机数生成方法 */
    (void)meth;
    return 0;
}

/*
 * 获取当前随机数生成方法
 */
const RAND_METHOD *TONGSUO_RAND_get_rand_method(void) {
    /* 返回NULL表示使用默认的硬件实现 */
    return NULL;
}

/*
 * 生成随机数用于种子（使用预测阻力随机数）
 */
void TONGSUO_CRYPTO_sysrand_for_seed(uint8_t *out, size_t requested) {
    if (trusty_rng_hw_rand(out, requested) != NO_ERROR) {
        abort();
    }
}

/*
 * 系统随机数生成
 */
void TONGSUO_CRYPTO_sysrand(uint8_t *out, size_t requested) {
    if (trusty_rng_hw_rand(out, requested) != NO_ERROR) {
        abort();
    }
}

#endif  /* __TRUSTY__ && __TEE__ */
