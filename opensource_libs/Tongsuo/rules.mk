# Copyright (C) 2024 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This file is used only by Trusty - Tongsuo integration

LOCAL_DIR := $(GET_LOCAL_DIR)
LOCAL_PATH := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

TARGET_ARCH := $(ARCH)
TARGET_2ND_ARCH := $(ARCH)

# Reset local variables
LOCAL_CFLAGS :=
LOCAL_C_INCLUDES :=
LOCAL_SRC_FILES :=
LOCAL_SRC_FILES_$(TARGET_ARCH) :=
LOCAL_SRC_FILES_$(TARGET_2ND_ARCH) :=
LOCAL_CFLAGS_$(TARGET_ARCH) :=
LOCAL_CFLAGS_$(TARGET_2ND_ARCH) :=
LOCAL_ADDITIONAL_DEPENDENCIES :=

# get target_c_flags, target_c_includes, target_src_files
MODULE_SRCDEPS += $(LOCAL_DIR)/crypto-sources.mk
include $(LOCAL_DIR)/crypto-sources.mk

# Tongsuo特定配置
MODULE_COMPILEFLAGS += -D__linux__ -D__TRUSTY__ -D__TEE__
MODULE_COMPILEFLAGS += -DTONGSUO_IMPLEMENTATION
MODULE_COMPILEFLAGS += -DOPENSSL_NO_STDIO -DOPENSSL_NO_SOCK -DOPENSSL_NO_THREADS
MODULE_COMPILEFLAGS += -DOPENSSL_NO_SECURE_MEMORY -DOPENSSL_NO_MLOCK
MODULE_COMPILEFLAGS += -DOPENSSL_API_COMPAT=0x10101000L
MODULE_COMPILEFLAGS += -DSYMBOL_PREFIX=TONGSUO_
MODULE_COMPILEFLAGS += -DOPENSSLDIR=\"/etc/ssl\"
MODULE_COMPILEFLAGS += -DENGINESDIR=\"/usr/lib/engines\"
MODULE_COMPILEFLAGS += -DMODULESDIR=\"/usr/lib/ossl-modules\"

# 禁用C11原子操作（与musl兼容）
MODULE_CFLAGS += -D__STDC_NO_ATOMICS__

# 优化和安全标志
MODULE_CFLAGS += -Os -ffunction-sections -fdata-sections
MODULE_CFLAGS += -fPIC -DOPENSSL_USE_NODELETE -DOPENSSL_PIC
MODULE_CFLAGS += -DNDEBUG

# ARM架构特定配置
ifeq ($(ARCH),arm64)
MODULE_CFLAGS += -DOPENSSL_STATIC_ARMCAP
# 禁用汇编优化，使用纯C实现
MODULE_CFLAGS += -DOPENSSL_NO_ASM
endif

ifeq ($(ARCH),arm)
MODULE_CFLAGS += -DOPENSSL_STATIC_ARMCAP
# 禁用汇编优化，使用纯C实现
MODULE_CFLAGS += -DOPENSSL_NO_ASM
endif

# 如果禁用浮点运算，过滤掉相关文件
ifeq (false,$(call TOBOOL,$(ALLOW_FP_USE)))
# 过滤掉可能使用NEON指令的文件
# Tongsuo已经配置为no-asm，所以这里主要是预防性措施
endif

# 添加源文件
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(LOCAL_SRC_FILES))
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(LOCAL_SRC_FILES_$(ARCH)))

# 包含目录
MODULE_INCLUDES += $(LOCAL_DIR)
MODULE_INCLUDES += $(LOCAL_DIR)/crypto
MODULE_INCLUDES += $(LOCAL_DIR)/include
MODULE_INCLUDES += $(LOCAL_DIR)/providers/common/include
MODULE_INCLUDES += $(LOCAL_DIR)/providers/implementations/include

# 导出包含目录 - 注释掉以避免与BoringSSL冲突
# MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

# Trusty特定的stub实现
# 这里可以添加Tongsuo特定的stub实现
# include user/base/lib/tongsuo-stubs/tongsuo-stubs-inc.mk

include make/rctee_lib.mk
