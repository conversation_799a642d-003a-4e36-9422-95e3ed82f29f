/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo测试TA
 * 验证Tongsuo加密库在rctee TEE环境中的功能
 */

#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <rctee_user_ipc.h>
#include <uapi/err.h>
#include <libutee.h>
#define TLOG_LVL TLOG_LVL_INFO
#include <trusty_log.h>
#include <unistd.h>

// Tongsuo加密库头文件 - 根据官方文档使用正确的头文件
#include <openssl/evp.h>
#include <openssl/err.h>
#include <openssl/ec.h>
#include <openssl/rand.h>

#define TONGSUO_PORT "8aaaf200-2450-11e4-abe2-0002a5d5c51b"
#define TLOG_TAG "tongsuo_test"
#define TONGSUO_MAX_BUFFER_LENGTH 1024

#define TEE_TEST_CMD_SM3_TEST                 1
#define TEE_TEST_CMD_SM4_TEST                 2
#define TEE_TEST_CMD_SM2_TEST                 3
#define TEE_TEST_CMD_RAND_TEST                4

// 函数声明
int tongsuo_test_handle_cmd(uint32_t cmd,
                           uint8_t* in_buf,
                           size_t in_buf_size,
                           uint8_t** out_buf,
                           size_t* out_buf_size);

// 辅助函数：打印十六进制数据
static void print_hex(const char *label, const unsigned char *data, size_t len) {
    TLOGI("%s (%zu bytes): ", label, len);
    for (size_t i = 0; i < len; i++) {
        if (i > 0 && i % 16 == 0) {
            TLOGI("\n");
        }
        TLOGI("%02x ", data[i]);
    }
    TLOGI("\n");
}



// SM3哈希算法测试 - 使用官方推荐的EVP接口
static int test_sm3_hash(void) {
    TLOGI("=== 测试SM3哈希算法 ===\n");

    const char *test_data = "abc";
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len = 0;

    // 使用EVP接口进行SM3哈希计算
    EVP_MD_CTX *mdctx = EVP_MD_CTX_new();
    if (mdctx == NULL) {
        TLOGE("❌ EVP_MD_CTX_new失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_DigestInit_ex(mdctx, EVP_sm3(), NULL)) {
        TLOGE("❌ EVP_DigestInit_ex失败\n");
        EVP_MD_CTX_free(mdctx);
        return ERR_GENERIC;
    }

    if (!EVP_DigestUpdate(mdctx, test_data, strlen(test_data))) {
        TLOGE("❌ EVP_DigestUpdate失败\n");
        EVP_MD_CTX_free(mdctx);
        return ERR_GENERIC;
    }

    if (!EVP_DigestFinal_ex(mdctx, hash, &hash_len)) {
        TLOGE("❌ EVP_DigestFinal_ex失败\n");
        EVP_MD_CTX_free(mdctx);
        return ERR_GENERIC;
    }

    EVP_MD_CTX_free(mdctx);

    TLOGI("输入数据: %s\n", test_data);
    print_hex("SM3哈希结果", hash, hash_len);

    // 验证已知测试向量 (GM/T 0004-2012 Example 1)
    unsigned char expected[] = {
        0x66, 0xc7, 0xf0, 0xf4, 0x62, 0xee, 0xed, 0xd9,
        0xd1, 0xf2, 0xd4, 0x6b, 0xdc, 0x10, 0xe4, 0xe2,
        0x41, 0x67, 0xc4, 0x87, 0x5c, 0xf2, 0xf7, 0xa2,
        0x29, 0x7d, 0xa0, 0x2b, 0x8f, 0x4b, 0xa8, 0xe0
    };

    if (hash_len == 32 && memcmp(hash, expected, 32) == 0) {
        TLOGI("✅ SM3哈希测试通过\n");
        return TEE_SUCCESS;
    } else {
        TLOGE("❌ SM3哈希结果不匹配\n");
        return ERR_GENERIC;
    }
}

// SM4对称加密测试 - 使用官方推荐的EVP接口和CBC模式
static int test_sm4_cipher(void) {
    TLOGI("=== 测试SM4对称加密 ===\n");

    // 测试密钥和IV
    unsigned char key[16] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF,
        0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10
    };

    unsigned char iv[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F
    };

    const char *plaintext = "Winter Is Coming";
    size_t plaintext_len = strlen(plaintext);
    unsigned char ciphertext[64];
    unsigned char decrypted[64];
    int ciphertext_len = 0, decrypted_len = 0;
    int tmplen = 0;

    // 加密
    EVP_CIPHER_CTX *encrypt_ctx = EVP_CIPHER_CTX_new();
    if (encrypt_ctx == NULL) {
        TLOGE("❌ EVP_CIPHER_CTX_new失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_EncryptInit_ex(encrypt_ctx, EVP_sm4_cbc(), NULL, key, iv)) {
        TLOGE("❌ EVP_EncryptInit_ex失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_EncryptUpdate(encrypt_ctx, ciphertext, &ciphertext_len,
                          (unsigned char*)plaintext, plaintext_len)) {
        TLOGE("❌ EVP_EncryptUpdate失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_EncryptFinal_ex(encrypt_ctx, ciphertext + ciphertext_len, &tmplen)) {
        TLOGE("❌ EVP_EncryptFinal_ex失败\n");
        EVP_CIPHER_CTX_free(encrypt_ctx);
        return ERR_GENERIC;
    }
    ciphertext_len += tmplen;
    EVP_CIPHER_CTX_free(encrypt_ctx);

    TLOGI("明文: %s\n", plaintext);
    print_hex("SM4加密结果", ciphertext, ciphertext_len);

    // 解密
    EVP_CIPHER_CTX *decrypt_ctx = EVP_CIPHER_CTX_new();
    if (decrypt_ctx == NULL) {
        TLOGE("❌ EVP_CIPHER_CTX_new失败\n");
        return ERR_GENERIC;
    }

    if (!EVP_DecryptInit_ex(decrypt_ctx, EVP_sm4_cbc(), NULL, key, iv)) {
        TLOGE("❌ EVP_DecryptInit_ex失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_DecryptUpdate(decrypt_ctx, decrypted, &decrypted_len,
                          ciphertext, ciphertext_len)) {
        TLOGE("❌ EVP_DecryptUpdate失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }

    if (!EVP_DecryptFinal_ex(decrypt_ctx, decrypted + decrypted_len, &tmplen)) {
        TLOGE("❌ EVP_DecryptFinal_ex失败\n");
        EVP_CIPHER_CTX_free(decrypt_ctx);
        return ERR_GENERIC;
    }
    decrypted_len += tmplen;
    EVP_CIPHER_CTX_free(decrypt_ctx);

    // 添加字符串结束符
    decrypted[decrypted_len] = '\0';
    TLOGI("解密结果: %s\n", (char*)decrypted);

    // 验证解密结果
    if (decrypted_len == plaintext_len &&
        memcmp(plaintext, decrypted, plaintext_len) == 0) {
        TLOGI("✅ SM4加密/解密测试通过\n");
        return TEE_SUCCESS;
    } else {
        TLOGE("❌ SM4解密结果不匹配\n");
        return ERR_GENERIC;
    }
}

// SM2椭圆曲线测试 - 使用官方推荐的EVP接口
static int test_sm2_basic(void) {
    TLOGI("=== 测试SM2椭圆曲线基础功能 ===\n");

    // 生成SM2密钥对 - 使用EVP接口
    EVP_PKEY_CTX *pkey_ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_EC, NULL);
    if (pkey_ctx == NULL) {
        TLOGE("❌ EVP_PKEY_CTX_new_id失败\n");
        return ERR_GENERIC;
    }

    if (EVP_PKEY_keygen_init(pkey_ctx) <= 0) {
        TLOGE("❌ EVP_PKEY_keygen_init失败\n");
        EVP_PKEY_CTX_free(pkey_ctx);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_CTX_set_ec_paramgen_curve_nid(pkey_ctx, OBJ_sn2nid("SM2")) <= 0) {
        TLOGE("❌ EVP_PKEY_CTX_set_ec_paramgen_curve_nid失败\n");
        EVP_PKEY_CTX_free(pkey_ctx);
        return ERR_GENERIC;
    }

    EVP_PKEY *sm2_key = NULL;
    if (EVP_PKEY_keygen(pkey_ctx, &sm2_key) <= 0) {
        TLOGE("❌ EVP_PKEY_keygen失败\n");
        EVP_PKEY_CTX_free(pkey_ctx);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_set_alias_type(sm2_key, EVP_PKEY_SM2) <= 0) {
        TLOGE("❌ EVP_PKEY_set_alias_type失败\n");
        EVP_PKEY_free(sm2_key);
        EVP_PKEY_CTX_free(pkey_ctx);
        return ERR_GENERIC;
    }

    EVP_PKEY_CTX_free(pkey_ctx);

    TLOGI("✅ SM2密钥对生成成功\n");

    // 测试SM2加密/解密
    const char *plaintext = "Hello SM2";
    size_t plaintext_len = strlen(plaintext);
    unsigned char *ciphertext = NULL;
    size_t ciphertext_len = 0;

    // 加密
    EVP_PKEY_CTX *encrypt_ctx = EVP_PKEY_CTX_new(sm2_key, NULL);
    if (encrypt_ctx == NULL) {
        TLOGE("❌ 创建加密上下文失败\n");
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_encrypt_init(encrypt_ctx) <= 0) {
        TLOGE("❌ EVP_PKEY_encrypt_init失败\n");
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    // 获取密文长度
    if (EVP_PKEY_encrypt(encrypt_ctx, NULL, &ciphertext_len,
                        (unsigned char*)plaintext, plaintext_len) <= 0) {
        TLOGE("❌ 获取密文长度失败\n");
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    ciphertext = malloc(ciphertext_len);
    if (ciphertext == NULL) {
        TLOGE("❌ 分配密文内存失败\n");
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(sm2_key);
        return ERR_NO_MEMORY;
    }

    // 执行加密
    if (EVP_PKEY_encrypt(encrypt_ctx, ciphertext, &ciphertext_len,
                        (unsigned char*)plaintext, plaintext_len) <= 0) {
        TLOGE("❌ SM2加密失败\n");
        free(ciphertext);
        EVP_PKEY_CTX_free(encrypt_ctx);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    EVP_PKEY_CTX_free(encrypt_ctx);

    TLOGI("明文: %s\n", plaintext);
    print_hex("SM2加密结果", ciphertext, ciphertext_len);

    // 解密
    EVP_PKEY_CTX *decrypt_ctx = EVP_PKEY_CTX_new(sm2_key, NULL);
    if (decrypt_ctx == NULL) {
        TLOGE("❌ 创建解密上下文失败\n");
        free(ciphertext);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    if (EVP_PKEY_decrypt_init(decrypt_ctx) <= 0) {
        TLOGE("❌ EVP_PKEY_decrypt_init失败\n");
        EVP_PKEY_CTX_free(decrypt_ctx);
        free(ciphertext);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    unsigned char *decrypted = NULL;
    size_t decrypted_len = 0;

    // 获取明文长度
    if (EVP_PKEY_decrypt(decrypt_ctx, NULL, &decrypted_len,
                        ciphertext, ciphertext_len) <= 0) {
        TLOGE("❌ 获取明文长度失败\n");
        EVP_PKEY_CTX_free(decrypt_ctx);
        free(ciphertext);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    decrypted = malloc(decrypted_len + 1);
    if (decrypted == NULL) {
        TLOGE("❌ 分配明文内存失败\n");
        EVP_PKEY_CTX_free(decrypt_ctx);
        free(ciphertext);
        EVP_PKEY_free(sm2_key);
        return ERR_NO_MEMORY;
    }

    // 执行解密
    if (EVP_PKEY_decrypt(decrypt_ctx, decrypted, &decrypted_len,
                        ciphertext, ciphertext_len) <= 0) {
        TLOGE("❌ SM2解密失败\n");
        free(decrypted);
        EVP_PKEY_CTX_free(decrypt_ctx);
        free(ciphertext);
        EVP_PKEY_free(sm2_key);
        return ERR_GENERIC;
    }

    decrypted[decrypted_len] = '\0';
    TLOGI("解密结果: %s\n", (char*)decrypted);

    // 验证结果
    int result = TEE_SUCCESS;
    if (decrypted_len != plaintext_len ||
        memcmp(plaintext, decrypted, plaintext_len) != 0) {
        TLOGE("❌ SM2解密结果不匹配\n");
        result = ERR_GENERIC;
    } else {
        TLOGI("✅ SM2加密/解密测试通过\n");
    }

    // 清理资源
    free(decrypted);
    free(ciphertext);
    EVP_PKEY_CTX_free(decrypt_ctx);
    EVP_PKEY_free(sm2_key);

    if (result == TEE_SUCCESS) {
        TLOGI("✅ SM2基础功能测试通过\n");
    }

    return result;
}

// 删除AES测试，专注于Tongsuo SM算法

static int test_random_generation(void) {
    TLOGI("=== 测试随机数生成 ===\n");

    unsigned char random_bytes[32];

    if (RAND_bytes(random_bytes, sizeof(random_bytes)) != 1) {
        TLOGE("❌ RAND_bytes失败\n");
        return ERR_GENERIC;
    }

    print_hex("随机数", random_bytes, sizeof(random_bytes));

    // 简单检查：确保不是全零
    bool all_zero = true;
    for (size_t i = 0; i < sizeof(random_bytes); i++) {
        if (random_bytes[i] != 0) {
            all_zero = false;
            break;
        }
    }

    if (all_zero) {
        TLOGE("❌ 随机数生成失败（全零）\n");
        return ERR_GENERIC;
    }

    TLOGI("✅ 随机数生成测试通过\n");
    return TEE_SUCCESS;
}

// SM2测试函数暂时移除，等基本功能稳定后再添加

// rctee TA命令处理函数
int tongsuo_test_handle_cmd(uint32_t cmd,
                           uint8_t* in_buf,
                           size_t in_buf_size,
                           uint8_t** out_buf,
                           size_t* out_buf_size) {

    TLOGI("Tongsuo测试TA命令调用: %u\n", cmd);

    switch (cmd) {
    case TEE_TEST_CMD_SM3_TEST:
        TLOGI("处理SM3哈希测试命令\n");
        return test_sm3_hash();

    case TEE_TEST_CMD_SM4_TEST:
        TLOGI("处理SM4加密测试命令\n");
        return test_sm4_cipher();

    case TEE_TEST_CMD_SM2_TEST:
        TLOGI("处理SM2椭圆曲线测试命令\n");
        return test_sm2_basic();

    case TEE_TEST_CMD_RAND_TEST:
        TLOGI("处理随机数生成测试命令\n");
        return test_random_generation();

    default:
        TLOGE("未知命令: %u\n", cmd);
        return ERR_INVALID_ARGS;
    }
}

// rctee TA入口点函数
int RCTEE_OnConnect(void) {
    TLOGI("Tongsuo测试TA客户端连接\n");
    return TEE_SUCCESS;
}

void RCTEE_OnDisConnect(void* cookie) {
    TLOGI("Tongsuo测试TA客户端断开连接\n");
}

int RCTEE_OnInit(void) {
    TLOGI("Tongsuo测试TA初始化\n");

    // 运行所有测试
    TLOGI("==========================================\n");
    TLOGI("    Tongsuo rctee TEE 集成测试\n");
    TLOGI("==========================================\n");

    int result = TEE_SUCCESS;

    // 测试随机数生成
    if (test_random_generation() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试SM3哈希
    if (test_sm3_hash() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试SM4加密
    if (test_sm4_cipher() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    // 测试SM2椭圆曲线
    if (test_sm2_basic() != TEE_SUCCESS) {
        result = ERR_GENERIC;
    }

    if (result == TEE_SUCCESS) {
        TLOGI("==========================================\n");
        TLOGI("    ✅ 所有Tongsuo测试通过！\n");
        TLOGI("==========================================\n");
    } else {
        TLOGI("==========================================\n");
        TLOGI("    ❌ 部分Tongsuo测试失败！\n");
        TLOGI("==========================================\n");
    }

    return result;
}

// 注意: main函数由libutee库提供，这里不需要定义
