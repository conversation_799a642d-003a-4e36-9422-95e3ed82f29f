#ifndef USER_TA_HEADER_DEFINES_TONGSUO_TEST
#define USER_TA_HEADER_DEFINES_TONGSUO_TEST

#include <stddef.h>
#include <stdbool.h>
#include <stdint.h>
#include <rctee_user_ipc.h>

#define TA_UUID \
    { 0x8aaaf200, 0x2450, 0x11e4, \
        {0xab, 0xe2, 0x00, 0x02, 0xa5, 0xd5, 0xc5, 0x1b} }
        
/* 权限定义 */
#define TA_ALLOW_CONNECT_PERM (IPC_PORT_ALLOW_NS_CONNECT)

/* 属性值定义 */
static const bool ta_single_instance = true;
static const uint32_t ta_version = 0x0100;
static const char ta_description[] = "Tongsuo Crypto Library Test Application";
static const uint64_t ta_stack_size = 16384;

/* TA属性表 */
#define TA_CURRENT_TA_EXT_PROPERTIES \
    { "ta.singleInstance", USER_TA_PROP_TYPE_BOOL, &ta_single_instance }, \
    { "ta.version", USER_TA_PROP_TYPE_U32, &ta_version }, \
    { "ta.description", USER_TA_PROP_TYPE_STRING, ta_description }, \
    { "ta.stackSize", USER_TA_PROP_TYPE_U64, &ta_stack_size }

#endif /* USER_TA_HEADER_DEFINES_TONGSUO_TEST */
